[project]
name = 'stacks-decentralized-lottery'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.admin-and-pausing]
path = 'contracts/admin-and-pausing.clar'
clarity_version = 3
epoch = 3.1

[contracts.errors]
path = 'contracts/errors.clar'
clarity_version = 3
epoch = 3.1

[contracts.fee-collector]
path = 'contracts/fee-collector.clar'
clarity_version = 3
epoch = 3.1

[contracts.lottery-core]
path = 'contracts/lottery-core.clar'
clarity_version = 3
epoch = 3.1

[contracts.randomness]
path = 'contracts/randomness.clar'
clarity_version = 3
epoch = 3.1

[contracts.ticket-nft]
path = 'contracts/ticket-nft.clar'
clarity_version = 3
epoch = 3.1

[contracts.utils]
path = 'contracts/utils.clar'
clarity_version = 3
epoch = 3.1

[contracts.views]
path = 'contracts/views.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
